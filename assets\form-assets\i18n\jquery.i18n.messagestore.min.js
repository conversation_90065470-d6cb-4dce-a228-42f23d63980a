!function (a) { "use strict"; function e() { this.messages = {}, this.sources = {} } e.prototype = { load: function (e, s) { var n, r, t = null, o = [], i = this; if ("string" == typeof e) return a.i18n.log("Loading messages from: " + e), (n = e, r = a.Deferred(), a.getJSON(n).done(r.resolve).fail(function (e, s, t) { a.i18n.log("Error in loading messages from " + n + " Exception: " + t), r.resolve() }), r.promise()).then(function (e) { return i.load(e, s) }); if (s) return i.set(s, e), a.Deferred().resolve(); for (t in e) Object.prototype.hasOwnProperty.call(e, t) && (s = t, o.push(i.load(e[t], s))); return a.when.apply(a, o) }, set: function (e, s) { this.messages[e] ? this.messages[e] = a.extend(this.messages[e], s) : this.messages[e] = s }, get: function (e, s) { return this.messages[e] && this.messages[e][s] } }, a.extend(a.i18n.messageStore, new e) }(jQuery);