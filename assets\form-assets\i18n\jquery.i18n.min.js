!function (s) { "use strict"; var i, l = Array.prototype.slice; (i = function (e) { this.options = s.extend({}, i.defaults, e), this.parser = this.options.parser, this.locale = this.options.locale, this.messageStore = this.options.messageStore, this.languages = {} }).prototype = { localize: function (e) { var a, n, t, o, i, l; for (t = this.locale, o = 0; t;) { n = (a = t.split("-")).length; do { if (i = a.slice(0, n).join("-"), l = this.messageStore.get(i, e)) return l; n-- } while (n); if (t === this.options.fallbackLocale) break; t = s.i18n.fallbacks[this.locale] && s.i18n.fallbacks[this.locale][o] || this.options.fallbackLocale, s.i18n.log("Trying fallback locale for " + this.locale + ": " + t + " (" + e + ")"), o++ } return "" }, destroy: function () { s.removeData(document, "i18n") }, load: function (e, a) { var n, t, o, i = {}; if (e || a || (e = "i18n/" + s.i18n().locale + ".json", a = s.i18n().locale), "string" != typeof e || "json" === e.split("?")[0].split(".").pop()) return this.messageStore.load(e, a); for (i[a] = e + "/" + a + ".json", n = (s.i18n.fallbacks[a] || []).concat(this.options.fallbackLocale), t = 0; t < n.length; t++)i[o = n[t]] = e + "/" + o + ".json"; return this.load(i) }, parse: function (e, a) { var n = this.localize(e); return this.parser.language = s.i18n.languages[s.i18n().locale] || s.i18n.languages.default, "" === n && (n = e), this.parser.parse(n, a) } }, s.i18n = function (e, a) { var n, t = s.data(document, "i18n"), o = "object" == typeof e && e; return o && o.locale && t && t.locale !== o.locale && (t.locale = o.locale), t || (t = new i(o), s.data(document, "i18n", t)), "string" == typeof e ? (n = void 0 !== a ? l.call(arguments, 1) : [], t.parse(e, n)) : t }, s.fn.i18n = function () { var l = s.data(document, "i18n"); return l || (l = new i, s.data(document, "i18n", l)), this.each(function () { var e, a, n, t, o = s(this), i = o.data("i18n"); i ? (e = i.indexOf("["), a = i.indexOf("]"), -1 !== e && -1 !== a && e < a ? (n = i.slice(e + 1, a), t = i.slice(a + 1), "html" === n ? o.html(l.parse(t)) : o.attr(n, l.parse(t))) : o.text(l.parse(i))) : o.find("[data-i18n]").i18n() }) }, s.i18n.languages = {}, s.i18n.messageStore = s.i18n.messageStore || {}, s.i18n.parser = { parse: function (e, t) { return e.replace(/\$(\d+)/g, function (e, a) { var n = parseInt(a, 10) - 1; return void 0 !== t[n] ? t[n] : "$" + a }) }, emitter: {} }, s.i18n.fallbacks = {}, s.i18n.debug = !1, s.i18n.log = function () { window.console && s.i18n.debug && window.console.log.apply(window.console, arguments) }, i.defaults = { locale: s("html").attr("lang") || navigator.language || navigator.userLanguage || "", fallbackLocale: "en", parser: s.i18n.parser, messageStore: s.i18n.messageStore }, s.i18n.constructor = i }(jQuery);