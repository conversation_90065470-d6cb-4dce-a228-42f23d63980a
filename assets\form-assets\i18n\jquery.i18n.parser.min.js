!function (t) { "use strict"; function n(n) { this.options = t.extend({}, t.i18n.parser.defaults, n), this.language = t.i18n.languages[String.locale] || t.i18n.languages.default, this.emitter = t.i18n.parser.emitter } n.prototype = { constructor: n, simpleParse: function (n, u) { return n.replace(/\$(\d+)/g, function (n, r) { var t = parseInt(r, 10) - 1; return void 0 !== u[t] ? u[t] : "$" + r }) }, parse: function (n, r) { return n.indexOf("{{") < 0 ? this.simpleParse(n, r) : (this.emitter.language = t.i18n.languages[t.i18n().locale] || t.i18n.languages.default, this.emitter.emit(this.ast(n), r)) }, ast: function (u) { var t, r, l, e, i, a, n, o, c, f, s, g, v, h, p, d, m, $, x, C, A = 0; function P(t) { return function () { var n, r; for (n = 0; n < t.length; n++)if (null !== (r = t[n]())) return r; return null } } function j(n) { var r, t, u = A, l = []; for (r = 0; r < n.length; r++) { if (null === (t = n[r]())) return A = u, null; l.push(t) } return l } function w(u, l) { return function () { for (var n = A, r = [], t = l(); null !== t;)r.push(t), t = l(); return r.length < u ? (A = n, null) : r } } function E(r) { var t = r.length; return function () { var n = null; return u.slice(A, A + t) === r && (n = r, A += t), n } } function O(r) { return function () { var n = u.slice(A).match(r); return null === n ? null : (A += n[0].length, n[0]) } } function S() { var n = j([l, e]); return null === n ? null : n[1] } function y() { var n = j([i, a]); return null === n ? null : ["REPLACE", parseInt(n[1], 10) - 1] } function F() { var n, r = j([t, w(0, d)]); return null === r ? null : 1 < (n = r[1]).length ? ["CONCAT"].concat(n) : n[0] } function I() { var n = j([g, r, y]); return null === n ? null : [n[0], n[2]] } function N() { var n = j([g, r, d]); return null === n ? null : [n[0], n[2]] } function T() { var n = j([v, s, h]); return null === n ? null : n[1] } if (t = E("|"), r = E(":"), l = E("\\"), e = O(/^./), i = E("$"), a = O(/^\d+/), n = O(/^[^{}[\]$\\]/), o = O(/^[^{}[\]$\\|]/), P([S, O(/^[^{}[\]$\s]/)]), c = P([S, o]), f = P([S, n]), $ = O(/^[ !"$&'()*,./0-9;=?@A-Z^_`a-z~\x80-\xFF+-]+/), x = function (n) { return n.toString() }, g = function () { var n = $(); return null === n ? null : x(n) }, s = P([function () { var n = j([P([I, N]), w(0, F)]); return null === n ? null : n[0].concat(n[1]) }, function () { var n = j([g, w(0, F)]); return null === n ? null : [n[0]].concat(n[1]) }]), v = E("{{"), h = E("}}"), p = P([T, y, function () { var n = w(1, f)(); return null === n ? null : n.join("") }]), d = P([T, y, function () { var n = w(1, c)(); return null === n ? null : n.join("") }]), null === (m = null === (C = w(0, p)()) ? null : ["CONCAT"].concat(C)) || A !== u.length) throw new Error("Parse error at position " + A.toString() + " in input: " + u); return m } }, t.extend(t.i18n.parser, new n) }(jQuery);